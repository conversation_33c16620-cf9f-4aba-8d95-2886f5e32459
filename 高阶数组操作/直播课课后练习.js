const studentData = [
  {
    id: 1,
    name: "<PERSON>",
    age: 20,
    gender: "female",
    major: "Computer Science",
    gpa: 3.8,
    status: "active",
    enrolledDate: "2022-09-01",
    courses: [
      { code: "CS101", name: "Intro to Programming", score: 95 },
      { code: "MATH101", name: "Calculus I", score: 90 },
    ],
    address: { city: "Beijing", country: "China" },
    isGraduated: false,
    advisor: null,
  },
  {
    id: 2,
    name: "<PERSON>",
    age: 22,
    gender: "male",
    major: "Mechanical Engineering",
    gpa: 3.2,
    status: "on leave",
    enrolledDate: "2021-09-01",
    courses: [
      { code: "ME101", name: "St<PERSON><PERSON>", score: 70 },
      { code: "PHYS101", name: "<PERSON> I", score: 80 },
    ],
    address: { city: "Shanghai", country: "China" },
    isGraduated: false,
    advisor: "Dr. <PERSON>",
  },
  {
    id: 3,
    name: "<PERSON>",
    age: 21,
    gender: "male",
    major: "Computer Science",
    gpa: 3.5,
    status: "active",
    enrolledDate: "2021-09-01",
    courses: [
      { code: "CS102", name: "Data Structures", score: 88 },
      { code: "CS201", name: "<PERSON><PERSON><PERSON><PERSON>", score: 75 },
      { code: "MATH101", name: "Calculus I", score: 82 },
    ],
    address: { city: "New York", country: "USA" },
    isGraduated: false,
    advisor: "Dr. Wang",
  },
  {
    id: 4,
    name: "Diana",
    age: 23,
    gender: "female",
    major: "Mathematics",
    gpa: 3.9,
    status: "graduated",
    enrolledDate: "2020-09-01",
    courses: [
      { code: "MATH201", name: "Linear Algebra", score: 98 },
      { code: "STAT101", name: "Probability", score: 93 },
    ],
    address: { city: "London", country: "UK" },
    isGraduated: true,
    advisor: "Dr. Brown",
  },
  {
    id: 5,
    name: "Eve",
    age: 20,
    gender: "female",
    major: "Physics",
    gpa: 3.4,
    status: "active",
    enrolledDate: "2022-09-01",
    courses: [
      { code: "PHYS101", name: "Physics I", score: 85 },
      { code: "PHYS102", name: "Physics II", score: 76 },
    ],
    address: { city: "Tokyo", country: "Japan" },
    isGraduated: false,
    advisor: null,
  },
  {
    id: 6,
    name: "Frank",
    age: 24,
    gender: "male",
    major: "Computer Science",
    gpa: 3.1,
    status: "graduated",
    enrolledDate: "2019-09-01",
    courses: [
      { code: "CS301", name: "Operating Systems", score: 73 },
      { code: "CS401", name: "Distributed Systems", score: 65 },
    ],
    address: { city: "Berlin", country: "Germany" },
    isGraduated: true,
    advisor: "Dr. Smith",
  },
  {
    id: 7,
    name: "Grace",
    age: 21,
    gender: "female",
    major: "Chemistry",
    gpa: 3.6,
    status: "active",
    enrolledDate: "2022-09-01",
    courses: [
      { code: "CHEM101", name: "General Chemistry", score: 89 },
      { code: "CHEM201", name: "Organic Chemistry", score: 78 },
    ],
    address: { city: "Paris", country: "France" },
    isGraduated: false,
    advisor: "Dr. Lin",
  },
  {
    id: 8,
    name: "Hank",
    age: 25,
    gender: "male",
    major: "Mathematics",
    gpa: 3.7,
    status: "graduated",
    enrolledDate: "2018-09-01",
    courses: [
      { code: "MATH301", name: "Abstract Algebra", score: 91 },
      { code: "MATH401", name: "Topology", score: 87 },
    ],
    address: { city: "Rome", country: "Italy" },
    isGraduated: true,
    advisor: "Dr. Brown",
  },
  {
    id: 9,
    name: "Ivy",
    age: 19,
    gender: "female",
    major: "Biology",
    gpa: 3.3,
    status: "active",
    enrolledDate: "2023-09-01",
    courses: [],
    address: { city: "Seoul", country: "Korea" },
    isGraduated: false,
    advisor: null,
  },
  {
    id: 10,
    name: "Jake",
    age: 22,
    gender: "male",
    major: "Physics",
    gpa: 2.9,
    status: "dismissed",
    enrolledDate: "2020-09-01",
    courses: [{ code: "PHYS201", name: "Thermodynamics", score: 60 }],
    address: { city: "Madrid", country: "Spain" },
    isGraduated: false,
    advisor: null,
  },
  {
    id: 11,
    name: "Kate",
    age: 20,
    gender: "female",
    major: "Computer Science",
    gpa: 3.85,
    status: "active",
    enrolledDate: "2022-09-01",
    courses: [
      { code: "CS101", name: "Intro to Programming", score: 96 },
      { code: "CS102", name: "Data Structures", score: 91 },
    ],
    address: { city: "Toronto", country: "Canada" },
    isGraduated: false,
    advisor: "Dr. Wang",
  },
  {
    id: 12,
    name: "Leo",
    age: 23,
    gender: "male",
    major: "Psychology",
    gpa: 3.2,
    status: "active",
    enrolledDate: "2021-09-01",
    courses: [
      { code: "PSY101", name: "Intro to Psychology", score: 85 },
      { code: "PSY201", name: "Cognitive Psychology", score: 78 },
    ],
    address: { city: "Amsterdam", country: "Netherlands" },
    isGraduated: false,
    advisor: "Dr. Rose",
  },
  {
    id: 13,
    name: "Mia",
    age: 21,
    gender: "female",
    major: "Law",
    gpa: 3.6,
    status: "active",
    enrolledDate: "2022-09-01",
    courses: [
      { code: "LAW101", name: "Constitutional Law", score: 90 },
      { code: "LAW201", name: "Civil Law", score: 88 },
    ],
    address: { city: "Sydney", country: "Australia" },
    isGraduated: false,
    advisor: "Dr. White",
  },
  {
    id: 14,
    name: "Nick",
    age: 26,
    gender: "male",
    major: "Economics",
    gpa: 2.7,
    status: "on leave",
    enrolledDate: "2018-09-01",
    courses: [],
    address: { city: "Vienna", country: "Austria" },
    isGraduated: false,
    advisor: "Dr. Hill",
  },
  {
    id: 15,
    name: "Olivia",
    age: 24,
    gender: "female",
    major: "Art",
    gpa: 3.9,
    status: "graduated",
    enrolledDate: "2019-09-01",
    courses: [
      { code: "ART101", name: "Art History", score: 94 },
      { code: "ART201", name: "Painting", score: 97 },
    ],
    address: { city: "Los Angeles", country: "USA" },
    isGraduated: true,
    advisor: "Dr. Gray",
  },
];
// 下边这个url是数组操作方法的文档 包含语法 例子
// https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Global_Objects/Array/filter#element
// 尽量不要用ai自己回顾一下课上讲到的数据操作方法 下边问题都可以用课上讲到的解决 想一下需要使用哪些数组操作方法
// • 找到major为CS的所有学生
// • 同时是gpa大于3.5 age大于22
// • 找到修过MATH101的学生
// • 统计学生gpa平均值
// • 提取所有学生的名字和gpa
// • 找出gpa大于3.5 且已毕业的第一个学生
// • 是否有学生来自China
// • 是否所有学生都至少修了一门课
// • 某学生是否选修了 CS101
// • 根据gpa进行排序

("====================================复杂数组操作======================================================");
// • 按国家分组学生
// • 统计CS专业的平均 GPA
// • 找出所有在读（status === "active"）的学生姓名数组，并标注他们gpa是否大于3.5（gpa > 3.6）
// • 找出所有学了 CS101 课程且成绩高于 90 的学生姓名列表
// • 找出所有有课程成绩不及格的学生 以及他们的名字和不及格课程代码
